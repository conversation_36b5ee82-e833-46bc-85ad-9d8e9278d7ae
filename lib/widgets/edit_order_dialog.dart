import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:packagingwala_web/widgets/smart_svg_icon.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/country_picker.dart';
import '../widgets/platform_icon.dart';
import '../models/order_model.dart';
import '../controllers/dashboard_card_detail_controller.dart';

class EditOrderDialog extends StatelessWidget {
  final OrderModel order;
  final Function(OrderModel) onSave;

  const EditOrderDialog({
    super.key,
    required this.order,
    required this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DashboardCardDetailController>();
    MySize().init(context);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size40,
      ),
      child: Container(
        // width: MySize.size700,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(MySize.size24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer Information Section
                    _buildCustomerInformationSection(controller),

                    SizedBox(height: MySize.size24),

                    // Order Details Section
                    _buildOrderDetailsSection(controller),

                    SizedBox(height: MySize.size24),

                    // Shipping Address Section
                    _buildShippingAddressSection(controller),
                  ],
                ),
              ),
            ),

            // Footer Buttons
            _buildFooterButtons(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: SmartIcon(
              assetPath: 'assets/icons/edit_icon.svg',
              height: MySize.size20,
              width: MySize.size20,
              color: AppColors.primaryColor,
            ),
          ),
          SizedBox(width: MySize.size12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Edit Order',
                style: TextStyle(
                  fontSize: MySize.size20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                'Update order details for ${order.orderId}',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInformationSection(DashboardCardDetailController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            PlatformIcon(
              iconName: 'person',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Customer Information',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // Customer Name and Mobile Number Row
        Row(
          children: [
            // Customer Name
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Customer Name',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: MySize.size8),
                  CustomTextField(
                    controller: controller.editCustomerNameController,
                    hintText: 'Enter customer name',
                    fillColor: Colors.white,
                    borderColor: AppColors.borderColor,
                    borderRadius: MySize.size8,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: MySize.size16,
                      vertical: MySize.size12,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: MySize.size16),

            // Mobile Number
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Mobile Number',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: MySize.size8),
                  Row(
                    children: [
                      // Country Picker
                      Obx(
                        () => CountryPicker(
                          selectedCountry: controller.editSelectedCountry.value,
                          onCountrySelected: controller.selectEditCountry,
                          width: MySize.size62,
                        ),
                      ),
                      SizedBox(width: MySize.size8),
                      // Mobile Number Field
                      Expanded(
                        child: CustomTextField(
                          controller: controller.editMobileNumberController,
                          hintText: 'Enter mobile number',
                          keyboardType: TextInputType.phone,
                          fillColor: Colors.white,
                          borderColor: AppColors.borderColor,
                          borderRadius: MySize.size8,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: MySize.size16,
                            vertical: MySize.size12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderDetailsSection(DashboardCardDetailController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            PlatformIcon(
              iconName: 'order',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Order Details',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // First Row: Delivery Date and Order Status
        Row(
          children: [
            // Delivery Date
            Expanded(
              child: _buildFormField(
                label: 'Delivery Date',
                controller: controller.editDeliveryDateController,
                hintText: 'Select delivery date',
              ),
            ),

            SizedBox(width: MySize.size16),

            // Order Status
            Expanded(
              child: _buildFormField(
                label: 'Order Status',
                controller: controller.editOrderStatusController,
                hintText: 'Enter order status',
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // Second Row: Order Amount and Tracking ID
        Row(
          children: [
            // Order Amount
            Expanded(
              child: _buildFormField(
                label: 'Order Amount',
                controller: controller.editOrderAmountController,
                hintText: 'Enter order amount',
                keyboardType: TextInputType.number,
              ),
            ),

            SizedBox(width: MySize.size16),

            // Tracking ID
            Expanded(
              child: _buildFormField(
                label: 'Tracking Id',
                controller: controller.editTrackingIdController,
                hintText: 'Enter tracking ID',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildShippingAddressSection(DashboardCardDetailController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            PlatformIcon(
              iconName: 'location',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Shipping Address',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // First Row: Street Address and City
        Row(
          children: [
            // Street Address
            Expanded(
              child: _buildFormField(
                label: 'Street Address',
                controller: controller.editStreetAddressController,
                hintText: 'Enter street address',
              ),
            ),

            SizedBox(width: MySize.size16),

            // City
            Expanded(
              child: _buildFormField(
                label: 'City',
                controller: controller.editCityController,
                hintText: 'Enter city',
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // Second Row: State and Pin Code
        Row(
          children: [
            // State
            Expanded(
              child: _buildFormField(
                label: 'State',
                controller: controller.editStateController,
                hintText: 'Enter state',
              ),
            ),

            SizedBox(width: MySize.size16),

            // Pin Code
            Expanded(
              child: _buildFormField(
                label: 'Pin Code',
                controller: controller.editPinCodeController,
                hintText: 'Enter pin code',
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderItemCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: AppColors.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              _getProductName(),
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Text(
            '₹ ${order.amount.toStringAsFixed(0)}',
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  // Get product name based on order data
  String _getProductName() {
    // Generate product name based on order characteristics
    final productTypes = [
      'Custom Packaging Box',
      'Premium Gift Box',
      'Eco-Friendly Package',
      'Luxury Product Box',
      'Standard Shipping Box',
      'Branded Packaging',
      'Corrugated Box',
      'Display Package',
    ];

    // Use order ID to consistently generate the same product name for the same order
    final index = order.orderId.hashCode.abs() % productTypes.length;
    return productTypes[index];
  }

  Widget _buildFooterButtons(DashboardCardDetailController controller) {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Items Heading
          Row(
            children: [
              SmartIcon(
                assetPath: 'assets/icons/order_icon.svg',
                height: MySize.size20,
                width: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Order Items',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size16),

          // Responsive Order Item Card and Buttons Layout
          LayoutBuilder(
            builder: (context, constraints) {
              final isMobile = constraints.maxWidth <= 600;

              if (isMobile) {
                // Mobile Layout: Card on top, buttons below
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Order Item Card
                    _buildOrderItemCard(),

                    SizedBox(height: MySize.size16),

                    // Buttons Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _buildCancelButton(),
                        SizedBox(width: MySize.size12),
                        _buildSaveButton(controller),
                      ],
                    ),
                  ],
                );
              } else {
                // Desktop Layout: Card on left, buttons on right
                return Row(
                  children: [
                    // Order Item Card on the left
                    Expanded(
                      child: _buildOrderItemCard(),
                    ),

                    SizedBox(width: MySize.size24),

                    // Buttons on the right
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildCancelButton(),
                        SizedBox(width: MySize.size12),
                        _buildSaveButton(controller),
                      ],
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCancelButton() {
    return OutlinedButton(
      onPressed: () => Get.back(),
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: AppColors.borderColor),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size24,
          vertical: MySize.size12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
      ),
      child: Text(
        'Cancel',
        style: TextStyle(
          fontSize: MySize.size14,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildSaveButton(DashboardCardDetailController controller) {
    return ElevatedButton(
      onPressed: () => _handleSave(controller),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size24,
          vertical: MySize.size12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
      ),
      child: Text(
        'Save changes',
        style: TextStyle(
          fontSize: MySize.size14,
          fontWeight: FontWeight.w500,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        CustomTextField(
          controller: controller,
          hintText: hintText,
          keyboardType: keyboardType,
          fillColor: Colors.white,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size16,
            vertical: MySize.size12,
          ),
        ),
      ],
    );
  }

  void _handleSave(DashboardCardDetailController controller) {
    // Create updated order with new data
    final updatedOrder = OrderModel(
      orderId: order.orderId,
      customerName: controller.editCustomerNameController.text,
      customerPhone: '${controller.editSelectedCountry.value.dialCode}${controller.editMobileNumberController.text}',
      orderDate: order.orderDate, // Keep original order date
      deliveryDate: _parseDate(controller.editDeliveryDateController.text) ?? order.deliveryDate,
      itemCount: order.itemCount, // Keep original item count
      amount: double.tryParse(controller.editOrderAmountController.text) ?? order.amount,
      status: controller.editOrderStatusController.text,
    );

    onSave(updatedOrder);
  }

  DateTime? _parseDate(String dateString) {
    try {
      // Parse date in DD/MM/YYYY format
      final parts = dateString.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      // Return null if parsing fails
    }
    return null;
  }
}