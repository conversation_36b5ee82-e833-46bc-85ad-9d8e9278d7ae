import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/country_picker.dart';
import '../widgets/platform_icon.dart';
import '../models/order_model.dart';
import '../models/country_model.dart';

class StandaloneEditOrderController extends GetxController {
  // Text controllers
  final TextEditingController customerNameController = TextEditingController();
  final TextEditingController mobileNumberController = TextEditingController();
  final TextEditingController deliveryDateController = TextEditingController();
  final TextEditingController orderStatusController = TextEditingController();
  final TextEditingController orderAmountController = TextEditingController();
  final TextEditingController trackingIdController = TextEditingController();

  // Observable variables
  final Rx<CountryModel> selectedCountry = CountryModel(
    name: 'India',
    flag: '🇮🇳',
    code: 'IN',
    dialCode: '+91',
  ).obs;

  @override
  void onClose() {
    customerNameController.dispose();
    mobileNumberController.dispose();
    deliveryDateController.dispose();
    orderStatusController.dispose();
    orderAmountController.dispose();
    trackingIdController.dispose();
    super.onClose();
  }

  void prefillForm(OrderModel order) {
    customerNameController.text = order.customerName;
    mobileNumberController.text = order.customerPhone.replaceAll('+91', '').trim();
    deliveryDateController.text = _formatDateForInput(order.deliveryDate);
    orderStatusController.text = order.status;
    orderAmountController.text = order.amount.toString();
    trackingIdController.text = 'TRK${order.orderId.replaceAll('ORD-', '')}';
  }

  String _formatDateForInput(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void onCountrySelected(CountryModel country) {
    selectedCountry.value = country;
  }
}

class StandaloneEditOrderDialog extends StatelessWidget {
  final OrderModel order;
  final Function(OrderModel) onSave;

  const StandaloneEditOrderDialog({
    super.key,
    required this.order,
    required this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    final controller = Get.put(StandaloneEditOrderController());
    
    // Prefill form with order data
    controller.prefillForm(order);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size40,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(context),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(MySize.size24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer Information Section
                    _buildCustomerInformationSection(controller),

                    SizedBox(height: MySize.size24),

                    // Order Details Section
                    _buildOrderDetailsSection(controller),

                    SizedBox(height: MySize.size24),

                    // Shipping Address Section
                    _buildShippingAddressSection(),
                  ],
                ),
              ),
            ),

            // Footer Buttons
            _buildFooterButtons(context, controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(MySize.size16),
          topRight: Radius.circular(MySize.size16),
        ),
      ),
      child: Row(
        children: [
          PlatformIcon(
            iconName: 'edit',
            size: MySize.size24,
            color: AppColors.primaryColor,
          ),
          SizedBox(width: MySize.size12),
          Expanded(
            child: Text(
              'Edit Order - ${order.orderId}',
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              Get.delete<StandaloneEditOrderController>();
              Navigator.of(context).pop();
            },
            child: Container(
              padding: EdgeInsets.all(MySize.size8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
              child: PlatformIcon(
                iconName: 'close',
                size: MySize.size16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInformationSection(StandaloneEditOrderController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Customer Information',
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size16),
        Row(
          children: [
            // Customer Name
            Expanded(
              child: CustomTextField(
                controller: controller.customerNameController,
                labelText: 'Customer Name',
                hintText: 'Enter customer name',
                prefixIcon: PlatformIcon(
                  iconName: 'person',
                  size: MySize.size20,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            SizedBox(width: MySize.size16),
            // Mobile Number with Country Picker
            Expanded(
              child: Row(
                children: [
                  Obx(() => CountryPicker(
                    selectedCountry: controller.selectedCountry.value,
                    onCountrySelected: controller.onCountrySelected,
                    width: MySize.size80,
                  )),
                  SizedBox(width: MySize.size8),
                  Expanded(
                    child: CustomTextField(
                      controller: controller.mobileNumberController,
                      labelText: 'Mobile Number',
                      hintText: 'Enter mobile number',
                      keyboardType: TextInputType.phone,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderDetailsSection(StandaloneEditOrderController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Details',
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size16),
        Row(
          children: [
            // Delivery Date
            Expanded(
              child: CustomTextField(
                controller: controller.deliveryDateController,
                labelText: 'Delivery Date',
                hintText: 'DD/MM/YYYY',
                prefixIcon: PlatformIcon(
                  iconName: 'calendar',
                  size: MySize.size20,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            SizedBox(width: MySize.size16),
            // Order Status
            Expanded(
              child: CustomTextField(
                controller: controller.orderStatusController,
                labelText: 'Order Status',
                hintText: 'Enter order status',
                prefixIcon: PlatformIcon(
                  iconName: 'package',
                  size: MySize.size20,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: MySize.size16),
        Row(
          children: [
            // Order Amount
            Expanded(
              child: CustomTextField(
                controller: controller.orderAmountController,
                labelText: 'Order Amount',
                hintText: 'Enter amount',
                keyboardType: TextInputType.number,
                prefixIcon: Text(
                  '₹',
                  style: TextStyle(
                    fontSize: MySize.size16,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
            SizedBox(width: MySize.size16),
            // Tracking ID
            Expanded(
              child: CustomTextField(
                controller: controller.trackingIdController,
                labelText: 'Tracking ID',
                hintText: 'Enter tracking ID',
                prefixIcon: PlatformIcon(
                  iconName: 'local_shipping',
                  size: MySize.size20,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildShippingAddressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Shipping Address',
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size16),
        Container(
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: AppColors.backgroundColor,
            borderRadius: BorderRadius.circular(MySize.size8),
            border: Border.all(color: AppColors.borderColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  PlatformIcon(
                    iconName: 'location',
                    size: MySize.size16,
                    color: AppColors.primaryColor,
                  ),
                  SizedBox(width: MySize.size8),
                  Text(
                    'Current Address',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: MySize.size8),
              Text(
                'Venkat Nagar 4th Street Yanam\nPincode: 533464',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFooterButtons(BuildContext context, StandaloneEditOrderController controller) {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(MySize.size16),
          bottomRight: Radius.circular(MySize.size16),
        ),
      ),
      child: Row(
        children: [
          // Cancel Button
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                Get.delete<StandaloneEditOrderController>();
                Navigator.of(context).pop();
              },
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppColors.borderColor),
                padding: EdgeInsets.symmetric(vertical: MySize.size12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
            ),
          ),

          SizedBox(width: MySize.size16),

          // Save Button
          Expanded(
            child: ElevatedButton(
              onPressed: () => _handleSave(context, controller),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                padding: EdgeInsets.symmetric(vertical: MySize.size12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                elevation: 0,
              ),
              child: Text(
                'Save Changes',
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleSave(BuildContext context, StandaloneEditOrderController controller) {
    // Create updated order with new data
    final updatedOrder = OrderModel(
      orderId: order.orderId,
      customerName: controller.customerNameController.text,
      customerPhone: '${controller.selectedCountry.value.dialCode}${controller.mobileNumberController.text}',
      orderDate: order.orderDate, // Keep original order date
      deliveryDate: _parseDate(controller.deliveryDateController.text) ?? order.deliveryDate,
      itemCount: order.itemCount, // Keep original item count
      amount: double.tryParse(controller.orderAmountController.text) ?? order.amount,
      status: controller.orderStatusController.text,
    );

    Get.delete<StandaloneEditOrderController>();
    Navigator.of(context).pop();
    onSave(updatedOrder);
  }

  DateTime? _parseDate(String dateStr) {
    try {
      final parts = dateStr.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      // Invalid date format
    }
    return null;
  }
}
